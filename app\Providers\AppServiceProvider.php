<?php

namespace App\Providers;

use App\Models\TbScanlog;
use App\Observer\RealtimeObserver;
use App\Service\realTimeService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        TbScanlog::observe(RealtimeObserver::class);
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
