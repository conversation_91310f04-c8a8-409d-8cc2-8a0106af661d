<?php

// Import Sample Data for FingerSpot Database
echo "Importing FingerSpot Sample Data\n";
echo "================================\n\n";

// Bootstrap Laravel
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    echo "✓ Laravel bootstrapped\n";
} else {
    echo "✗ Laravel not found\n";
    exit(1);
}

try {
    // Test database connection
    $pdo = DB::connection()->getPdo();
    echo "✓ Database connected\n";
    
    // Import device data
    echo "\n=== Importing Device Data ===\n";
    DB::table('tb_device')->insert([
        'server_IP' => '************',
        'server_port' => '8080',
        'device_sn' => '66595015390139',
        'created_at' => now(),
        'updated_at' => now()
    ]);
    echo "✓ Device data imported\n";
    
    // Import user data
    echo "\n=== Importing User Data ===\n";
    $users = [
        ['pin' => '1', 'nama' => 'Admin', 'pwd' => '', 'rfid' => '', 'privilege' => 14],
        ['pin' => '2', 'nama' => 'User 2', 'pwd' => '', 'rfid' => '', 'privilege' => 0],
        ['pin' => '3', 'nama' => 'User 3', 'pwd' => '', 'rfid' => '', 'privilege' => 0],
        ['pin' => '4', 'nama' => 'User 4', 'pwd' => '', 'rfid' => '', 'privilege' => 0],
        ['pin' => '5', 'nama' => 'User 5', 'pwd' => '', 'rfid' => '', 'privilege' => 0]
    ];
    
    foreach ($users as $user) {
        DB::table('tb_user')->insert($user);
    }
    echo "✓ " . count($users) . " users imported\n";
    
    // Import sample scan logs
    echo "\n=== Importing Scan Log Data ===\n";
    $scanlogs = [
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 09:00:00', 'pin' => '1', 'verifymode' => 1, 'iomode' => 1, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 09:15:00', 'pin' => '2', 'verifymode' => 1, 'iomode' => 1, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 09:30:00', 'pin' => '3', 'verifymode' => 1, 'iomode' => 1, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 12:00:00', 'pin' => '1', 'verifymode' => 1, 'iomode' => 0, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 12:15:00', 'pin' => '2', 'verifymode' => 1, 'iomode' => 0, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 17:00:00', 'pin' => '1', 'verifymode' => 1, 'iomode' => 1, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 17:30:00', 'pin' => '4', 'verifymode' => 1, 'iomode' => 1, 'workcode' => 0],
        ['sn' => '66595015390139', 'scan_date' => '2024-06-14 18:00:00', 'pin' => '5', 'verifymode' => 1, 'iomode' => 0, 'workcode' => 0]
    ];
    
    foreach ($scanlogs as $log) {
        DB::table('tb_scanlog')->insert($log);
    }
    echo "✓ " . count($scanlogs) . " scan logs imported\n";
    
    // Import sample template (simplified)
    echo "\n=== Importing Template Data ===\n";
    $templates = [
        ['pin' => '1', 'finger_idx' => 0, 'alg_ver' => 39, 'template' => 'sample_template_data_1'],
        ['pin' => '1', 'finger_idx' => 1, 'alg_ver' => 39, 'template' => 'sample_template_data_2'],
        ['pin' => '2', 'finger_idx' => 0, 'alg_ver' => 39, 'template' => 'sample_template_data_3']
    ];
    
    foreach ($templates as $template) {
        DB::table('tb_template')->insert($template);
    }
    echo "✓ " . count($templates) . " templates imported\n";
    
    // Verify data
    echo "\n=== Verifying Imported Data ===\n";
    $deviceCount = DB::table('tb_device')->count();
    $userCount = DB::table('tb_user')->count();
    $scanlogCount = DB::table('tb_scanlog')->count();
    $templateCount = DB::table('tb_template')->count();
    
    echo "✓ Devices: $deviceCount\n";
    echo "✓ Users: $userCount\n";
    echo "✓ Scan logs: $scanlogCount\n";
    echo "✓ Templates: $templateCount\n";
    
    echo "\n=== Sample Data Summary ===\n";
    echo "🎉 Sample data imported successfully!\n";
    echo "✅ Device configuration ready\n";
    echo "✅ User accounts created\n";
    echo "✅ Attendance logs available\n";
    echo "✅ Biometric templates stored\n";
    
    echo "\n=== Test the Application ===\n";
    echo "1. Visit: http://localhost:8000/user\n";
    echo "2. Visit: http://localhost:8000/scanlog\n";
    echo "3. Visit: http://localhost:8000/setting\n";
    echo "4. Visit: http://localhost:8000/info\n";
    
} catch (Exception $e) {
    echo "✗ Error importing data: " . $e->getMessage() . "\n";
    
    // Check if tables exist
    try {
        $tables = DB::select('SHOW TABLES');
        echo "\nAvailable tables:\n";
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "- $tableName\n";
        }
    } catch (Exception $e2) {
        echo "Cannot check tables: " . $e2->getMessage() . "\n";
    }
}

echo "\nSample data import completed! 🚀\n";
