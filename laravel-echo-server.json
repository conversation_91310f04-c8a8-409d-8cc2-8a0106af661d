{"authHost": "http://localhost", "authEndpoint": "/broadcasting/auth", "clients": [], "database": "redis", "databaseConfig": {"redis": {"host": "localhost", "port": "6379"}, "sqlite": {"databasePath": "/database/laravel-echo-server.sqlite"}}, "devMode": true, "host": null, "port": "6005", "protocol": "http", "socketio": {}, "sslCertPath": "", "sslKeyPath": "", "sslCertChainPath": "", "sslPassphrase": "", "apiOriginAllow": {"allowCors": false, "allowOrigin": "", "allowMethods": "", "allowHeaders": ""}}