<?php

use App\Http\Controllers\FingerSpot\InfoController;
use App\Http\Controllers\FingerSpot\ScanLogController;
use App\Http\Controllers\FingerSpot\SettingController;
use App\Http\Controllers\FingerSpot\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// ROUTE UNTUK SETTING
Route::get('/setting', [SettingController::class, 'setting']);
Route::get('/proses_setting', [SettingController::class, 'prosesSetting']);

// ROUTE UNTUK USER
Route::get('/user', [UserController::class, 'user']);
Route::get('/user_proses', [UserController::class, 'userProses']);

// ROUTE UNTUK SCAN LOG
Route::get('/scanlog', [ScanLogController::class, 'scanLog']);
Route::get('/proses_scan', [ScanLogController::class, 'prosesScan']);

// ROUTE UNTUK INFO
Route::get('/info', [InfoController::class, 'info']);
Route::get('/proses_info', [InfoController::class, 'prosesInfo']);
