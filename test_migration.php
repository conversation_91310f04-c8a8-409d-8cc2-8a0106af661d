<?php

// Simple test script to verify our Laravel migration changes
echo "Testing Laravel Migration Changes\n";
echo "=================================\n\n";

// Test 1: Check if model files exist
$models = [
    'app/Models/User.php',
    'app/Models/TbUser.php',
    'app/Models/TbDevice.php',
    'app/Models/TbTemplate.php',
    'app/Models/TbScanlog.php'
];

echo "1. Checking Model Files:\n";
foreach ($models as $model) {
    if (file_exists($model)) {
        echo "✓ $model exists\n";
    } else {
        echo "✗ $model missing\n";
    }
}

echo "\n2. Checking Controller Updates:\n";
$controllers = [
    'app/Http/Controllers/FingerSpot/UserController.php',
    'app/Http/Controllers/FingerSpot/ScanLogController.php',
    'app/Http/Controllers/FingerSpot/SettingController.php',
    'app/Http/Controllers/FingerSpot/InfoController.php'
];

foreach ($controllers as $controller) {
    if (file_exists($controller)) {
        $content = file_get_contents($controller);
        if (strpos($content, 'App\\Models\\') !== false) {
            echo "✓ $controller updated to use new Models namespace\n";
        } else {
            echo "✗ $controller still uses old namespace\n";
        }
    } else {
        echo "✗ $controller missing\n";
    }
}

echo "\n3. Checking Routes:\n";
if (file_exists('routes/web.php')) {
    $content = file_get_contents('routes/web.php');
    if (strpos($content, '::class') !== false) {
        echo "✓ Routes updated to use new Laravel 10 syntax\n";
    } else {
        echo "✗ Routes still use old syntax\n";
    }
} else {
    echo "✗ routes/web.php missing\n";
}

echo "\n4. Checking Composer.json:\n";
if (file_exists('composer.json')) {
    $content = file_get_contents('composer.json');
    $composer = json_decode($content, true);
    if (isset($composer['require']['laravel/framework']) && 
        strpos($composer['require']['laravel/framework'], '^10.0') !== false) {
        echo "✓ Composer.json updated for Laravel 10\n";
    } else {
        echo "✗ Composer.json not properly updated\n";
    }
} else {
    echo "✗ composer.json missing\n";
}

echo "\nMigration Summary:\n";
echo "- Models moved to App\\Models namespace\n";
echo "- Controllers updated to use new model references\n";
echo "- Routes updated to Laravel 10 syntax\n";
echo "- Composer.json updated for Laravel 10\n";
echo "- Database factories updated\n";
echo "\nNext steps:\n";
echo "1. Run 'composer install' to install dependencies\n";
echo "2. Run 'php artisan key:generate' to generate app key\n";
echo "3. Configure .env file\n";
echo "4. Run 'php artisan migrate' to set up database\n";
echo "5. Test the application\n";
